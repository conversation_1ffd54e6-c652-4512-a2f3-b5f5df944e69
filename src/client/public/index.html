<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- 🎯 SEO优化和页面信息 -->
    <title>🚀 极光书库 - 现代化书库搜索下载平台</title>
    <meta name="description" content="现代化的夸克网盘搜索下载工具，采用国防级安全技术，支持智能搜索和高速下载">
    <meta name="keywords" content="夸克网盘,文件搜索,在线下载,现代化界面,安全下载">
    <meta name="author" content="极光书库团队">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#667eea">

    <!-- 🌐 Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://sk.djxs.xyz/">
    <meta property="og:title" content="🚀 极光书库 - 现代化书库搜索下载平台">
    <meta property="og:description" content="现代化的夸克网盘搜索下载工具，采用国防级安全技术，支持智能搜索和高速下载">
    <meta property="og:image" content="https://sk.djxs.xyz/favicon.ico">

    <!-- 🐦 Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://sk.djxs.xyz/">
    <meta property="twitter:title" content="🚀 极光书库 - 现代化书库搜索下载平台">
    <meta property="twitter:description" content="现代化的夸克网盘搜索下载工具，采用国防级安全技术，支持智能搜索和高速下载">
    <meta property="twitter:image" content="https://sk.djxs.xyz/favicon.ico">

    <!-- 📱 移动端优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="极光书库">

    <!-- 🎨 样式和字体资源 -->
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- 🔍 结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "极光书库",
        "description": "现代化的夸克网盘搜索下载工具",
        "url": "https://sk.djxs.xyz",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        },
        "author": {
            "@type": "Organization",
            "name": "极光书库团队"
        }
    }
    </script>
</head>
<body>
    <!-- 🚨 跳转到主内容的无障碍链接 -->
    <a href="#main-content" class="skip-to-content" aria-label="跳转到主要内容">跳转到主要内容</a>

    <div class="container">
        <!-- 🚨 认证过期提醒对话框 -->
        <div id="cookieExpiredAlert"
             class="cookie-expired-alert"
             style="display: none;"
             role="dialog"
             aria-modal="true"
             aria-labelledby="alert-title"
             aria-describedby="alert-description">
            <div class="alert-content">
                <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                <div class="alert-text">
                    <h3 id="alert-title">认证已过期</h3>
                    <p id="alert-description">下载功能需要有效的认证配置。请联系系统管理员更新认证信息。</p>
                    <div class="contact-info">
                        <p><strong>解决方案：</strong></p>
                        <ul>
                            <li>联系系统管理员</li>
                            <li>说明认证过期问题</li>
                            <li>等待管理员更新认证配置</li>
                            <li>认证更新后系统将自动恢复</li>
                        </ul>
                    </div>
                </div>
                <button onclick="app.dismissCookieAlert()"
                        aria-label="关闭认证过期提醒">我知道了</button>
            </div>
        </div>

        <!-- 🎯 页面头部 -->
        <header class="header" role="banner">
            <h1>
                <i class="fas fa-rocket" aria-hidden="true"></i>
                极光书库
            </h1>
            <div class="security-badge" role="img" aria-label="Aurora安全认证徽章">
                <i class="fas fa-shield-virus" aria-hidden="true"></i>
                <span>AURORA SECURE</span>
            </div>
        </header>

        <!-- 🏠 主要内容区域 -->
        <main id="main-content" class="main-content" role="main">
            <!-- 🔐 认证状态区域 -->
            <section class="section cookie-section"
                     aria-labelledby="auth-section-title"
                     role="region">
                <h2 id="auth-section-title">
                    <i class="fas fa-shield-alt" aria-hidden="true"></i>
                    认证状态
                </h2>
                <div class="auth-info-card">
                    <div class="auth-icon" role="img" aria-label="认证密钥图标">
                        <i class="fas fa-key" aria-hidden="true"></i>
                    </div>
                    <div class="auth-content">
                        <h3>系统认证</h3>
                        <p>认证配置由服务器安全管理</p>
                        <div class="cookie-status"
                             id="cookieStatus"
                             role="status"
                             aria-live="polite"
                             aria-atomic="true">
                            <i class="fas fa-info-circle" aria-hidden="true"></i>
                            正在初始化...
                        </div>
                    </div>
                </div>
                <div class="cookie-info" role="note">
                    <i class="fas fa-shield-check" aria-hidden="true"></i>
                    系统采用服务器端统一认证管理，保护用户隐私和账户安全。认证信息已加密存储。
                </div>
                <!-- 🔧 隐藏的技术支持元素 -->
                <textarea id="cookieInput"
                          style="display: none;"
                          readonly
                          aria-hidden="true"
                          tabindex="-1"></textarea>
                <button id="saveCookie"
                        style="display: none;"
                        aria-hidden="true"
                        tabindex="-1"></button>
                <button id="toggleEdit"
                        style="display: none;"
                        aria-hidden="true"
                        tabindex="-1"></button>
            </section>

            <!-- 🔍 文件搜索区域 -->
            <section class="section search-section"
                     aria-labelledby="search-section-title"
                     role="search">
                <h2 id="search-section-title">
                    <i class="fas fa-search" aria-hidden="true"></i>
                    文件搜索
                </h2>
                <form class="search-form"
                      role="search"
                      aria-label="文件搜索表单">
                    <div class="search-input-group">
                        <label for="searchKeyword" class="visually-hidden">搜索关键词</label>
                        <input type="text"
                               id="searchKeyword"
                               name="keyword"
                               placeholder="输入搜索关键词..."
                               class="search-input"
                               aria-describedby="search-help"
                               autocomplete="off"
                               spellcheck="false">
                        <button type="submit"
                                id="searchBtn"
                                class="btn btn-search"
                                aria-describedby="search-help">
                            <i class="fas fa-search" aria-hidden="true"></i>
                            <span>搜索</span>
                        </button>
                    </div>
                    <div id="search-help" class="visually-hidden">
                        输入文件名或关键词进行搜索，支持模糊匹配
                    </div>

                    <!-- 🎛️ 搜索选项 -->
                    <fieldset class="search-options" aria-label="搜索选项设置">
                        <legend class="visually-hidden">搜索参数配置</legend>

                        <div class="option-group">
                            <label for="loadMode">加载模式:</label>
                            <select id="loadMode"
                                    name="loadMode"
                                    aria-describedby="loadMode-help">
                                <option value="waterfall" selected>瀑布式加载</option>
                                <option value="pagination">分页模式</option>
                            </select>
                            <div id="loadMode-help" class="visually-hidden">
                                选择结果展示方式：瀑布式加载或传统分页
                            </div>
                        </div>

                        <div class="option-group">
                            <label for="pageSize">每页显示:</label>
                            <select id="pageSize"
                                    name="pageSize"
                                    aria-describedby="pageSize-help">
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                            </select>
                            <div id="pageSize-help" class="visually-hidden">
                                设置每页显示的搜索结果数量
                            </div>
                        </div>

                        <div class="option-group">
                            <label for="sortType">排序方式:</label>
                            <select id="sortType"
                                    name="sortType"
                                    aria-describedby="sortType-help">
                                <option value="file_type:desc,updated_at:desc">类型↓ 时间↓</option>
                                <option value="size:desc">大小↓</option>
                                <option value="size:asc">大小↑</option>
                                <option value="updated_at:desc">时间↓</option>
                                <option value="updated_at:asc">时间↑</option>
                            </select>
                            <div id="sortType-help" class="visually-hidden">
                                选择搜索结果的排序方式
                            </div>
                        </div>
                    </fieldset>
                </form>
            </section>

            <!-- 📊 搜索结果区域 -->
            <section class="section results-section"
                     id="resultsSection"
                     style="display: none;"
                     aria-labelledby="results-section-title"
                     role="region"
                     aria-live="polite">
                <h2 id="results-section-title">
                    <i class="fas fa-list" aria-hidden="true"></i>
                    搜索结果
                </h2>
                <div class="results-header">
                    <div class="results-info"
                         id="resultsInfo"
                         role="status"
                         aria-live="polite"></div>
                    <nav class="pagination"
                         id="pagination"
                         role="navigation"
                         aria-label="搜索结果分页导航"></nav>
                </div>
                <div class="results-list"
                     id="resultsList"
                     role="list"
                     aria-label="搜索结果列表"></div>
                <div class="load-more-container"
                     id="loadMoreContainer"
                     style="display: none;">
                    <button class="btn btn-load-more"
                            id="loadMoreBtn"
                            aria-describedby="loadMore-help">
                        <i class="fas fa-chevron-down" aria-hidden="true"></i>
                        加载更多
                    </button>
                    <div id="loadMore-help" class="visually-hidden">
                        点击加载更多搜索结果
                    </div>
                    <div class="load-more-info"
                         id="loadMoreInfo"
                         role="status"
                         aria-live="polite"></div>
                </div>
            </section>

            <!-- 📥 下载管理区域 -->
            <section class="section downloads-section"
                     id="downloadsSection"
                     style="display: none;"
                     aria-labelledby="downloads-section-title"
                     role="region">
                <h2 id="downloads-section-title">
                    <i class="fas fa-download" aria-hidden="true"></i>
                    下载管理
                </h2>
                <div class="downloads-list"
                     id="downloadsList"
                     role="list"
                     aria-label="下载任务列表"></div>
            </section>
        </main>

        <!-- ⏳ 加载指示器 -->
        <div class="loading"
             id="loading"
             style="display: none;"
             role="alert"
             aria-busy="true"
             aria-live="assertive">
            <div class="spinner" aria-hidden="true"></div>
            <p>正在处理中...</p>
        </div>
    </div>

    <!-- 🔄 无障碍跳转链接 -->
    <a href="#"
       class="back-to-top"
       aria-label="返回顶部"
       title="返回顶部">
        <i class="fas fa-arrow-up" aria-hidden="true"></i>
    </a>

    <!-- 📱 无障碍状态通知区域 -->
    <div id="a11y-announcer"
         class="visually-hidden"
         role="status"
         aria-live="polite"
         aria-atomic="true"></div>

    <!-- 📜 脚本资源 -->
    <script src="js/quark-api.js"></script>
    <script src="js/app.js"></script>

    <!-- 🔍 无障碍支持脚本 -->
    <script>
    // 初始化无障碍支持
    document.addEventListener('DOMContentLoaded', function() {
        // 添加键盘导航支持
        document.addEventListener('keydown', function(e) {
            // ESC键关闭弹窗
            if (e.key === 'Escape') {
                const alert = document.getElementById('cookieExpiredAlert');
                if (alert && alert.style.display !== 'none') {
                    app.dismissCookieAlert();
                }
            }

            // 回到顶部快捷键 (Alt+T)
            if (e.altKey && e.key === 't') {
                window.scrollTo({top: 0, behavior: 'smooth'});
                e.preventDefault();
            }
        });

        // 返回顶部按钮
        const backToTopBtn = document.querySelector('.back-to-top');
        if (backToTopBtn) {
            // 监听滚动显示/隐藏按钮
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.add('show');
                } else {
                    backToTopBtn.classList.remove('show');
                }
            });

            // 点击返回顶部
            backToTopBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({top: 0, behavior: 'smooth'});
            });
        }
    });
    </script>
</body>
</html>