/* 🎨 现代化夸克网盘界面样式 - 2024版 */
/* 🌟 极光书库 - 完美整理版 */
/* 📅 最后更新: 2024.07.28 */
/* 👨‍💻 作者: 极光书库团队 */

/* 🔧 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 🎯 无障碍访问支持 */
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* 🔗 跳转到主内容链接 */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-gradient);
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    z-index: 1000;
    transition: top 0.3s ease;
}

.skip-to-content:focus {
    top: 6px;
}

/* 🔝 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 999;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: var(--shadow-xl);
}

.back-to-top:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

/* 🎨 CSS变量系统 - 设计令牌 */
:root {
    /* 🌈 主题色彩系统 */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    --secondary-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
    --info-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* 🎯 基础颜色 */
    --primary-color: #667eea;
    --secondary-color: #4facfe;
    --success-color: #11998e;
    --warning-color: #f093fb;
    --danger-color: #fc466b;
    --info-color: #667eea;

    /* 📝 文字颜色系统 */
    --text-primary: #1a202c;
    --text-secondary: #2d3748;
    --text-muted: #718096;
    --text-light: #a0aec0;
    --text-white: #ffffff;
    --text-inverse: #ffffff;

    /* 🎨 背景颜色系统 */
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-muted: #edf2f7;
    --bg-glass: rgba(255, 255, 255, 0.25);
    --bg-glass-hover: rgba(255, 255, 255, 0.35);
    --bg-glass-active: rgba(255, 255, 255, 0.45);

    /* 🔲 边框颜色系统 */
    --border-light: rgba(255, 255, 255, 0.18);
    --border-medium: rgba(255, 255, 255, 0.3);
    --border-strong: rgba(255, 255, 255, 0.5);
    --border-focus: rgba(102, 126, 234, 0.5);

    /* 🌊 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* 📐 圆角系统 */
    --radius-none: 0;
    --radius-xs: 0.125rem;
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    --radius-full: 9999px;

    /* 📏 间距系统 */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* 📱 断点系统 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;

    /* ⚡ 动画系统 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-spring: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 🔤 字体系统 */
    --font-family-sans: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* 📏 字体大小系统 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;

    /* 📏 行高系统 */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* 🎯 Z-index系统 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* 🏗️ 基础布局样式 */
body {
    font-family: var(--font-family-sans);
    background: var(--primary-gradient);
    min-height: 100vh;
    color: var(--text-primary);
    position: relative;
    overflow-x: hidden;
    line-height: var(--leading-normal);
    font-size: var(--text-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 🎯 焦点管理 */
*:focus {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

*:focus:not(:focus-visible) {
    outline: none;
}

*:focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

/* 🌊 动态背景装饰系统 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    z-index: -1;
    will-change: transform, opacity;
}

/* 🎭 动画关键帧定义 */
@keyframes backgroundFloat {
    0%, 100% {
        opacity: 1;
        transform: translateY(0px) scale(1);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-20px) scale(1.02);
    }
}

/* 📦 容器布局系统 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-xl);
    position: relative;
    z-index: 1;
    width: 100%;
}

/* 📱 响应式容器 */
@media (max-width: 1440px) {
    .container {
        max-width: 1200px;
        padding: var(--space-lg);
    }
}

@media (max-width: 1024px) {
    .container {
        max-width: 100%;
        padding: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .container {
        padding: var(--space-md);
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--space-sm);
    }
}

/* 🎯 头部组件样式 */
.header {
    text-align: center;
    margin-bottom: var(--space-3xl);
    animation: fadeInDown 0.8s var(--transition-bounce);
    padding: var(--space-lg) 0;
}

/* 🎭 入场动画 */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 🎨 标题样式 */
.header h1 {
    color: var(--text-white);
    font-size: clamp(var(--text-2xl), 5vw, var(--text-5xl));
    font-weight: 800;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    margin-bottom: var(--space-lg);
    letter-spacing: -0.02em;
    line-height: var(--leading-tight);
    font-family: var(--font-family-sans);
}

.header h1 i {
    margin-right: var(--space-md);
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: iconGlow 2s ease-in-out infinite alternate;
    display: inline-block;
    will-change: filter;
}

/* 🌟 图标发光动画 */
@keyframes iconGlow {
    from {
        filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));
        transform: scale(1);
    }
    to {
        filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.8));
        transform: scale(1.05);
    }
}

/* 🛡️ 安全徽章组件 */
.security-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    background: var(--primary-gradient);
    color: var(--text-white);
    font-weight: 700;
    font-size: var(--text-sm);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    backdrop-filter: blur(10px);
    animation: securityPulse 3s ease-in-out infinite;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: var(--transition-normal);
    will-change: transform, box-shadow;
}

.security-badge:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

.security-badge i {
    font-size: var(--text-base);
    animation: securitySpin 4s linear infinite;
    will-change: transform;
}

/* 🔄 安全徽章动画 */
@keyframes securityPulse {
    0%, 100% {
        box-shadow: var(--shadow-lg);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 20px 40px -5px rgba(102, 126, 234, 0.4);
        transform: scale(1.05);
    }
}

@keyframes securitySpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 🏠 主要内容区域 */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 🎨 通用section样式 - 磨砂玻璃效果 */
.section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
}

.section:hover {
    background: var(--glass-hover);
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.section h2 {
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.01em;
}

.section h2 i {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--secondary-gradient);
    border-radius: var(--radius-md);
    color: white;
    font-size: 1rem;
    box-shadow: var(--shadow-md);
}

/* 🔐 认证卡片样式 */
.auth-info-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: var(--radius-xl);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.auth-info-card:hover {
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.1) 100%);
    transform: translateY(-1px);
}

.auth-icon {
    width: 4rem;
    height: 4rem;
    background: var(--success-gradient);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.auth-icon i {
    font-size: 1.5rem;
    color: white;
}

.auth-content {
    flex: 1;
}

.auth-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.auth-content p {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

/* 状态指示器 */
.cookie-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid;
    transition: all 0.3s ease;
}

.cookie-status.success {
    background: linear-gradient(135deg, rgba(17, 153, 142, 0.1), rgba(56, 239, 125, 0.1));
    color: #0f5132;
    border-color: rgba(56, 239, 125, 0.3);
}

.cookie-status.error {
    background: linear-gradient(135deg, rgba(252, 70, 107, 0.1), rgba(63, 94, 251, 0.1));
    color: #842029;
    border-color: rgba(252, 70, 107, 0.3);
}

.cookie-status.info {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
    color: #055160;
    border-color: rgba(79, 172, 254, 0.3);
}

.cookie-info {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.05));
    border: 1px solid rgba(79, 172, 254, 0.2);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    color: #055160;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    line-height: 1.5;
    margin-top: 1rem;
}

.cookie-info i {
    margin-top: 0.125rem;
    flex-shrink: 0;
    color: #0969da;
}

/* 🔍 搜索区域 */
.search-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.search-input-group {
    display: flex;
    gap: 1rem;
    align-items: stretch;
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: var(--text-dark);
    transition: all 0.3s ease;
    font-weight: 500;
}

.search-input::placeholder {
    color: rgba(45, 55, 72, 0.6);
}

.search-input:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.search-options {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.option-group label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.875rem;
}

.option-group select {
    padding: 0.625rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.option-group select:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    background: rgba(255, 255, 255, 0.2);
}

/* 🎨 按钮样式 */
.btn {
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: var(--radius-xl);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0) scale(0.98);
}

.btn-search {
    background: var(--success-gradient);
    color: white;
    padding: 1rem 2rem;
    font-size: 1rem;
    box-shadow: var(--shadow-md);
}

.btn-download {
    background: var(--warning-gradient);
    color: white;
    padding: 0.625rem 1.25rem;
    font-size: 0.75rem;
    border-radius: var(--radius-lg);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-secondary {
    background: rgba(108, 117, 125, 0.8);
    color: white;
    backdrop-filter: blur(10px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 📊 搜索结果区域 */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.results-info {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
}

.pagination {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.pagination button {
    padding: 0.5rem 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 0.875rem;
}

.pagination button:hover:not(:disabled) {
    background: var(--secondary-gradient);
    color: white;
    transform: translateY(-1px);
}

.pagination button.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
    box-shadow: var(--shadow-md);
}

.pagination button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

/* 📋 结果列表 */
.results-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.result-item {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.result-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--secondary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.result-item:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-xl);
}

.result-item:hover::before {
    transform: scaleX(1);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.result-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    flex: 1;
    word-break: break-all;
    line-height: 1.4;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.result-name i {
    color: #667eea;
    font-size: 1.25rem;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

.result-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.result-detail:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.result-detail i {
    width: 1rem;
    color: #667eea;
    text-align: center;
}

/* 📥 下载管理区域 */
.downloads-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.download-item {
    padding: 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.download-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.download-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.download-name {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.875rem;
}

.download-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.download-status.success {
    background: var(--success-gradient);
    color: white;
}

.download-status.error {
    background: var(--danger-gradient);
    color: white;
}

.download-status.pending {
    background: var(--warning-gradient);
    color: white;
}

/* 🔄 加载指示器 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: white;
}

.spinner {
    width: 4rem;
    height: 4rem;
    border: 4px solid rgba(255,255,255,0.2);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    margin-bottom: 1.5rem;
}

.loading p {
    font-size: 1.125rem;
    font-weight: 500;
    text-align: center;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 📬 消息提示 */
.toast {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    max-width: 400px;
    transform: translateX(120%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
    z-index: 1100;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    user-select: none;
}

.toast i {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.toast span {
    flex: 1;
    line-height: 1.4;
}

.toast.show {
    transform: translateX(0);
    animation: toastBounce 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes toastBounce {
    0% { transform: translateX(120%) scale(0.8); }
    60% { transform: translateX(-10px) scale(1.05); }
    100% { transform: translateX(0) scale(1); }
}

.toast:hover {
    transform: translateX(-5px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.toast.success {
    background: var(--success-gradient);
}

.toast.error {
    background: var(--danger-gradient);
}

.toast.info {
    background: var(--secondary-gradient);
}

.toast.warning {
    background: var(--warning-gradient);
}

/* 🚨 认证过期提醒 */
.cookie-expired-alert {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1200;
    padding: 1rem;
}

.alert-content {
    background: white;
    border-radius: var(--radius-2xl);
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    box-shadow: var(--shadow-xl);
    animation: alertSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.alert-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--danger-gradient);
}

@keyframes alertSlideIn {
    from {
        transform: scale(0.9) translateY(20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

.alert-content i {
    font-size: 3rem;
    color: #f56565;
    margin-bottom: 1rem;
    display: block;
    text-align: center;
}

.alert-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-dark);
    text-align: center;
}

.alert-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    text-align: center;
}

.alert-content button {
    width: 100%;
    padding: 1rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.alert-content button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 🚀 性能优化样式 */
.result-item {
    contain: layout style paint;
    will-change: transform;
}

.result-item img {
    content-visibility: auto;
    contain-intrinsic-size: 48px 48px;
}

/* 🎭 动画性能优化 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 🔋 电池优化 */
@media (prefers-reduced-motion: reduce) {
    .security-badge i {
        animation: none;
    }

    .header h1 i {
        animation: none;
    }

    body::before {
        animation: none;
    }
}

/* 📱 响应式设计 */
@media (max-width: 1024px) {
    .container {
        padding: var(--space-lg);
    }

    .main-content {
        gap: var(--space-lg);
    }

    .section {
        padding: var(--space-lg);
    }

    /* 🎯 触摸优化 */
    .btn {
        min-height: 44px;
        min-width: 44px;
    }

    .search-input {
        min-height: 44px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: var(--space-md);
    }

    .header h1 {
        font-size: var(--text-2xl);
    }

    .section {
        padding: var(--space-md);
    }

    .auth-info-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }

    .search-input-group {
        flex-direction: column;
        gap: var(--space-md);
    }

    .search-options {
        gap: var(--space-md);
        justify-content: center;
        flex-wrap: wrap;
    }

    .results-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: var(--space-md);
    }

    .result-header {
        flex-direction: column;
        gap: var(--space-md);
    }

    .result-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--space-sm);
    }

    .result-details {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    /* 📱 移动端优化 */
    .toast {
        left: var(--space-md);
        right: var(--space-md);
        max-width: none;
    }

    .pagination button {
        min-width: 40px;
        padding: var(--space-xs) var(--space-sm);
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--space-sm);
    }

    .header {
        margin-bottom: var(--space-xl);
    }

    .header h1 {
        font-size: var(--text-xl);
        margin-bottom: var(--space-md);
    }

    .btn {
        padding: var(--space-sm) var(--space-md);
        font-size: var(--text-xs);
        min-height: 44px;
    }

    .search-input {
        padding: var(--space-sm) var(--space-md);
        font-size: var(--text-sm);
        min-height: 44px;
    }

    .toast {
        top: var(--space-md);
        right: var(--space-md);
        left: var(--space-md);
        max-width: none;
    }

    .alert-content {
        margin: var(--space-md);
        padding: var(--space-lg);
    }

    .section {
        padding: var(--space-sm);
        margin-bottom: var(--space-md);
    }

    .security-badge {
        font-size: var(--text-xs);
        padding: var(--space-xs) var(--space-md);
    }

    /* 🔤 小屏幕字体优化 */
    .result-item h3 {
        font-size: var(--text-sm);
        line-height: var(--leading-tight);
    }

    .result-details {
        font-size: var(--text-xs);
    }
}

/* 🌊 瀑布式加载样式 */
.load-more-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-top: 30px;
    padding: 20px;
}

.btn-load-more {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-load-more:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-load-more:active {
    transform: translateY(0);
}

.btn-load-more i {
    margin-right: 8px;
    transition: transform 0.3s ease;
}

.btn-load-more:hover i {
    transform: translateY(2px);
}

.load-more-info {
    color: #666;
    font-size: 14px;
    text-align: center;
    opacity: 0.8;
}

/* 分页样式优化 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 20px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    background: white;
    color: #333;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.pagination button:hover:not(:disabled) {
    background: #f5f5f5;
    border-color: #007bff;
}

.pagination button.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination span {
    padding: 8px 4px;
    color: #666;
}

