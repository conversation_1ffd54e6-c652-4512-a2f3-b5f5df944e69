/**
 * 🚀 极光夸克网盘 - 现代化搜索下载工具
 * Aurora Quark Drive - Modern Search & Download Platform
 *
 * @fileoverview 极光书库主应用程序 - 提供现代化的文件搜索和下载功能
 * @version 2.0.0
 * <AUTHOR>
 * @since 2024.07.28
 * @license MIT
 *
 * @description
 * 这是一个现代化的夸克网盘搜索下载工具，采用国防级安全技术，
 * 支持智能搜索、高速下载、响应式设计和无障碍访问。
 *
 * 主要功能：
 * - 🔍 智能文件搜索
 * - 📥 高速文件下载
 * - 🎨 现代化UI设计
 * - 📱 完美响应式布局
 * - ♿ 无障碍访问支持
 * - 🛡️ 国防级安全保护
 *
 * @requires QuarkDriveAPI
 * @requires FontAwesome
 * @requires Modern Browser (ES6+)
 */

/**
 * 🎬 页面加载动效管理器
 * @namespace PageLoader
 * @description 管理页面加载动画和初始化效果
 */
const PageLoader = {
    /**
     * 创建并显示页面加载动画
     * @method createLoadingScreen
     * @description 创建一个全屏加载动画，包含火箭图标和进度条
     * @returns {HTMLElement} 加载屏幕元素
     * @since 2.0.0
     */
    createLoadingScreen() {
        const loadingScreen = document.createElement('div');
        loadingScreen.className = 'page-loading';
        loadingScreen.setAttribute('role', 'status');
        loadingScreen.setAttribute('aria-label', '页面加载中');
        loadingScreen.innerHTML = `
            <div class="loading-content">
                <div class="loading-logo" aria-hidden="true">
                    <i class="fas fa-rocket"></i>
                </div>
                <div class="loading-text">极光夸克</div>
                <div class="loading-bar" role="progressbar" aria-label="加载进度">
                    <div class="loading-progress"></div>
                </div>
            </div>
        `;
        return loadingScreen;
    },

    /**
     * 移除页面加载动画
     * @method removeLoadingScreen
     * @param {HTMLElement} loadingScreen - 要移除的加载屏幕元素
     * @param {number} [delay=1500] - 延迟时间（毫秒）
     * @description 使用淡出动画移除加载屏幕
     * @since 2.0.0
     */
    removeLoadingScreen(loadingScreen, delay = 1500) {
        setTimeout(() => {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                if (loadingScreen.parentNode) {
                    loadingScreen.remove();
                }
            }, 500);
        }, delay);
    },

    /**
     * 初始化页面加载动效
     * @method init
     * @description 在DOM加载完成后自动执行页面加载动画
     * @since 2.0.0
     */
    init() {
        const loadingScreen = this.createLoadingScreen();
        document.body.appendChild(loadingScreen);
        this.removeLoadingScreen(loadingScreen);
    }
};

/**
 * 🚀 性能优化管理器
 * @namespace PerformanceOptimizer
 * @description 管理页面性能优化和资源加载
 */
const PerformanceOptimizer = {
    /**
     * 预加载关键资源
     * @method preloadResources
     * @description 预加载页面所需的关键资源
     * @returns {void}
     * @since 2.0.0
     */
    preloadResources() {
        // 预加载字体图标
        if ('preload' in HTMLLinkElement.prototype) {
            const preloads = [
                { href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/webfonts/fa-solid-900.woff2', as: 'font', type: 'font/woff2', crossorigin: true },
                { href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap', as: 'style' }
            ];

            preloads.forEach(resource => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = resource.href;
                link.as = resource.as;
                if (resource.type) link.type = resource.type;
                if (resource.crossorigin) link.crossOrigin = 'anonymous';
                document.head.appendChild(link);
            });
        }
    },

    /**
     * 注册性能观察器
     * @method registerPerformanceObserver
     * @description 注册性能观察器以监控页面性能
     * @returns {void}
     * @since 2.0.0
     */
    registerPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            try {
                // 监控长任务
                const longTaskObserver = new PerformanceObserver(list => {
                    list.getEntries().forEach(entry => {
                        console.warn('🔥 检测到长任务:', entry.duration, 'ms');
                    });
                });
                longTaskObserver.observe({ entryTypes: ['longtask'] });

                // 监控布局偏移
                const layoutShiftObserver = new PerformanceObserver(list => {
                    list.getEntries().forEach(entry => {
                        if (entry.value > 0.1) {
                            console.warn('🔥 检测到布局偏移:', entry.value);
                        }
                    });
                });
                layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
            } catch (e) {
                console.error('性能观察器初始化失败:', e);
            }
        }
    },

    /**
     * 初始化性能优化
     * @method init
     * @description 初始化所有性能优化功能
     * @returns {void}
     * @since 2.0.0
     */
    init() {
        this.preloadResources();
        this.registerPerformanceObserver();

        // 使用requestIdleCallback在空闲时执行非关键任务
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                console.log('🚀 在浏览器空闲时初始化非关键功能');
                // 可以在这里添加非关键功能的初始化
            });
        }
    }
};

/**
 * 🛡️ 安全增强器
 * @namespace SecurityEnhancer
 * @description 提供额外的客户端安全保护措施
 */
const SecurityEnhancer = {
    /**
     * 输入验证和清理
     * @method sanitizeInput
     * @param {string} input - 要清理的输入
     * @returns {string} 清理后的输入
     * @description 清理用户输入，防止XSS攻击
     * @since 2.0.0
     */
    sanitizeInput(input) {
        if (typeof input !== 'string') return '';

        // 基本的HTML实体编码
        return input
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    },

    /**
     * 检测可疑活动
     * @method detectSuspiciousActivity
     * @description 检测并记录可疑的用户活动
     * @returns {void}
     * @since 2.0.0
     */
    detectSuspiciousActivity() {
        let requestCount = 0;
        const startTime = Date.now();

        // 监控请求频率
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            requestCount++;
            const currentTime = Date.now();
            const timeElapsed = currentTime - startTime;

            // 如果在10秒内发送超过20个请求，记录为可疑活动
            if (timeElapsed < 10000 && requestCount > 20) {
                console.warn('🚨 检测到可疑活动: 请求频率过高');
            }

            return originalFetch.apply(this, args);
        };
    },

    /**
     * 内容安全策略检查
     * @method checkCSP
     * @description 检查内容安全策略的实施情况
     * @returns {void}
     * @since 2.0.0
     */
    checkCSP() {
        // 检查是否有CSP头
        const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        if (!metaCSP) {
            console.warn('⚠️ 未检测到CSP元标签');
        }
    },

    /**
     * 初始化安全增强功能
     * @method init
     * @description 初始化所有安全增强功能
     * @returns {void}
     * @since 2.0.0
     */
    init() {
        this.detectSuspiciousActivity();
        this.checkCSP();

        // 禁用右键菜单（可选）
        document.addEventListener('contextmenu', (e) => {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
            }
        });

        // 禁用F12开发者工具（基础防护）
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'U')) {
                e.preventDefault();
                console.warn('🛡️ 开发者工具访问被阻止');
            }
        });

        console.log('🛡️ 安全增强功能已激活');
    }
};

// 🚀 页面加载时自动初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化加载动效
    PageLoader.init();

    // 初始化性能优化
    PerformanceOptimizer.init();

    // 初始化安全增强
    SecurityEnhancer.init();

    // 添加视差滚动效果
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.header');
        if (parallax) {
            parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // 添加鼠标跟随光效
    document.addEventListener('mousemove', (e) => {
        const cursor = document.querySelector('.cursor-glow');
        if (!cursor) {
            const glowElement = document.createElement('div');
            glowElement.className = 'cursor-glow';
            document.body.appendChild(glowElement);
        }

        const glow = document.querySelector('.cursor-glow');
        glow.style.left = e.clientX + 'px';
        glow.style.top = e.clientY + 'px';
    });
});

// 添加页面加载样式
const pageLoadingStyles = `
<style>
.page-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: rocketLaunch 2s ease-in-out infinite;
}

@keyframes rocketLaunch {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.loading-text {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 2rem;
    letter-spacing: 0.1em;
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #fff, #ffd700);
    border-radius: 2px;
    animation: loadingProgress 1.5s ease-in-out infinite;
}

@keyframes loadingProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

.cursor-glow {
    position: fixed;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transform: translate(-50%, -50%);
    transition: all 0.1s ease;
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', pageLoadingStyles);

/**
 * 🎯 极光夸克网盘主应用类
 * @class QuarkApp
 * @description 极光书库的核心应用类，管理搜索、下载和用户交互
 * @version 2.0.0
 * <AUTHOR>
 * @since 2024.07.28
 *
 * @example
 * // 创建应用实例
 * const app = new QuarkApp();
 *
 * @example
 * // 执行搜索
 * await app.performSearch();
 *
 * @example
 * // 下载文件
 * await app.downloadFile('file-id', 'filename.txt');
 */
class QuarkApp {
    /**
     * 创建QuarkApp实例
     * @constructor
     * @description 初始化应用的所有核心组件和状态
     * @since 2.0.0
     */
    constructor() {
        /**
         * API接口实例
         * @type {QuarkDriveAPI}
         * @description 用于与夸克网盘API通信的接口实例
         */
        this.api = new QuarkDriveAPI();

        /**
         * 当前页码
         * @type {number}
         * @default 1
         * @description 搜索结果的当前页码
         */
        this.currentPage = 1;

        /**
         * 当前搜索关键词
         * @type {string}
         * @default ''
         * @description 用户输入的搜索关键词
         */
        this.currentKeyword = '';

        /**
         * 当前页搜索结果
         * @type {Array<Object>}
         * @default []
         * @description 当前页的搜索结果数组
         */
        this.searchResults = [];

        /**
         * 所有已加载的搜索结果
         * @type {Array<Object>}
         * @default []
         * @description 存储所有已加载的搜索结果，用于瀑布式加载
         */
        this.allResults = [];

        /**
         * 搜索结果总数
         * @type {number}
         * @default 0
         * @description 搜索返回的结果总数
         */
        this.totalResults = 0;

        /**
         * 是否还有更多结果
         * @type {boolean}
         * @default false
         * @description 标识是否还有更多结果可以加载
         */
        this.hasMoreResults = false;

        /**
         * 是否正在加载
         * @type {boolean}
         * @default false
         * @description 防止重复请求的加载状态标识
         */
        this.isLoading = false;

        /**
         * 加载模式
         * @type {string}
         * @default 'waterfall'
         * @description 结果展示模式：'waterfall'(瀑布式) 或 'pagination'(分页)
         */
        this.loadMode = 'waterfall';

        /**
         * 下载任务管理器
         * @type {Map<string, Object>}
         * @default new Map()
         * @description 管理所有下载任务的状态和信息
         */
        this.downloads = new Map();

        // 🚀 初始化应用
        this.init();
    }

    /**
     * 初始化应用
     * @method init
     * @description 初始化应用的所有组件，包括事件绑定和UI初始化
     * @returns {void}
     * @since 2.0.0
     *
     * @example
     * const app = new QuarkApp();
     * // init() 会在构造函数中自动调用
     */
    init() {
        try {
            this.bindEvents();
            this.initUI();
            this.announceToScreenReader('极光书库已加载完成，可以开始搜索文件');
        } catch (error) {
            console.error('🔥 应用初始化失败:', error);
            this.showToast('应用初始化失败，请刷新页面重试', 'error');
        }
    }

    /**
     * 绑定事件监听器
     * @method bindEvents
     * @description 为所有交互元素绑定事件监听器，包括搜索、分页、下载等
     * @returns {void}
     * @since 2.0.0
     *
     * @throws {Error} 当必要的DOM元素不存在时抛出错误
     */
    bindEvents() {
        try {
            // 🔍 搜索相关事件
            const searchBtn = document.getElementById('searchBtn');
            const searchKeyword = document.getElementById('searchKeyword');
            const loadMode = document.getElementById('loadMode');
            const loadMoreBtn = document.getElementById('loadMoreBtn');

            // 验证必要元素存在
            if (!searchBtn || !searchKeyword || !loadMode || !loadMoreBtn) {
                throw new Error('缺少必要的DOM元素');
            }

            // 搜索按钮点击事件
            searchBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.performSearch();
            });

            // 搜索框回车事件
            searchKeyword.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });

            // 搜索框输入事件（实时搜索建议）
            searchKeyword.addEventListener('input', this.debounce((e) => {
                const keyword = e.target.value.trim();
                if (keyword.length > 2) {
                    // 可以在这里添加搜索建议功能
                    this.announceToScreenReader(`输入了 ${keyword.length} 个字符`);
                }
            }, 300));

            // 加载模式切换事件
            loadMode.addEventListener('change', (e) => {
                this.loadMode = e.target.value;
                this.updateUIForLoadMode();
                this.announceToScreenReader(`切换到${e.target.selectedOptions[0].text}模式`);
            });

            // 加载更多按钮事件
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreResults();
            });

        } catch (error) {
            console.error('🔥 事件绑定失败:', error);
            throw error;
        }

        // 📜 滚动加载（瀑布式加载模式）
        window.addEventListener('scroll', this.throttle(() => {
            if (this.loadMode === 'waterfall' && this.hasMoreResults && !this.isLoading) {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const windowHeight = window.innerHeight;
                const documentHeight = document.documentElement.scrollHeight;

                // 当滚动到距离底部200px时自动加载
                if (scrollTop + windowHeight >= documentHeight - 200) {
                    this.loadMoreResults();
                }
            }
        }, 200)); // 使用节流函数优化滚动事件

        // 🎮 键盘导航支持
        document.addEventListener('keydown', (e) => {
            // Alt+S 聚焦到搜索框
            if (e.altKey && e.key === 's') {
                e.preventDefault();
                const searchInput = document.getElementById('searchKeyword');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Alt+L 加载更多结果
            if (e.altKey && e.key === 'l') {
                e.preventDefault();
                if (this.hasMoreResults && !this.isLoading) {
                    this.loadMoreResults();
                }
            }
        });
    }

    /**
     * 节流函数 - 限制函数执行频率
     * @method throttle
     * @param {Function} func - 要执行的函数
     * @param {number} limit - 时间限制（毫秒）
     * @returns {Function} 节流后的函数
     * @description 确保函数在指定时间内最多执行一次，用于优化滚动等高频事件
     * @since 2.0.0
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 防抖函数 - 延迟函数执行
     * @method debounce
     * @param {Function} func - 要执行的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 防抖后的函数
     * @description 确保函数在连续调用后只执行一次，用于优化输入等事件
     * @since 2.0.0
     */
    debounce(func, delay) {
        let debounceTimer;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => func.apply(context, args), delay);
        };
    }

    /**
     * 初始化UI状态
     * @method initUI
     * @description 初始化用户界面状态，包括认证状态检查和UI组件设置
     * @returns {Promise<void>}
     * @since 2.0.0
     *
     * @throws {Error} 当UI初始化失败时抛出错误
     */
    async initUI() {
        try {
            // 🔐 首先尝试从服务器获取Cookie认证状态
            await this.loadServerCookies();

            // 🎨 初始化UI组件状态
            this.initializeUIComponents();

        } catch (error) {
            console.error('🔥 UI初始化失败:', error);
            this.showToast('界面初始化失败，部分功能可能不可用', 'warning');
        }
    }

    /**
     * 初始化UI组件
     * @method initializeUIComponents
     * @description 设置各种UI组件的初始状态和属性
     * @returns {void}
     * @since 2.0.0
     */
    initializeUIComponents() {
        // 设置搜索框占位符动画
        const searchInput = document.getElementById('searchKeyword');
        if (searchInput) {
            this.animatePlaceholder(searchInput);
        }

        // 初始化工具提示
        this.initializeTooltips();

        // 设置无障碍属性
        this.setupAccessibilityFeatures();
    }

    /**
     * 屏幕阅读器通知
     * @method announceToScreenReader
     * @param {string} message - 要通知的消息
     * @description 向屏幕阅读器用户通知重要信息
     * @returns {void}
     * @since 2.0.0
     */
    announceToScreenReader(message) {
        const announcer = document.getElementById('a11y-announcer');
        if (announcer) {
            announcer.textContent = message;
            // 清空消息，避免重复读取
            setTimeout(() => {
                announcer.textContent = '';
            }, 1000);
        }
    }

    /**
     * 设置无障碍功能
     * @method setupAccessibilityFeatures
     * @description 配置各种无障碍访问功能
     * @returns {void}
     * @since 2.0.0
     */
    setupAccessibilityFeatures() {
        // 为动态内容添加适当的ARIA属性
        const resultsList = document.getElementById('resultsList');
        if (resultsList) {
            resultsList.setAttribute('aria-live', 'polite');
        }

        // 设置键盘导航提示
        const searchInput = document.getElementById('searchKeyword');
        if (searchInput) {
            searchInput.setAttribute('aria-describedby', 'search-help');
        }
    }

    /**
     * 从服务器加载Cookie认证状态
     * @method loadServerCookies
     * @description 从服务器获取当前的认证状态并更新UI显示
     * @returns {Promise<void>}
     * @throws {Error} 当网络请求失败时抛出错误
     * @since 2.0.0
     *
     * @example
     * await app.loadServerCookies();
     */
    async loadServerCookies() {
        try {
            // 🔄 更新状态为验证中
            this.updateCookieStatus('info', '正在验证认证状态...');
            this.announceToScreenReader('正在验证认证状态');

            // 📡 发送认证状态请求
            const response = await fetch('/api/cookies', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.status === 'ok' && result.security && result.security.configured) {
                // 服务器确认认证已配置，显示安全信息
                const security = result.security;

                // 显示国防级安全信息
                const cookieInput = document.getElementById('cookieInput');
                cookieInput.value = `🛡️ 国防级安全认证系统\n\n` +
                    `安全等级：${security.level}\n` +
                    `加密算法：${security.encryption}\n` +
                    `配置状态：${security.configured ? '已配置' : '未配置'}\n` +
                    `管理类型：${security.type}\n` +
                    `配置数量：${security.count} 项\n` +
                    `配置哈希：${security.hash}\n` +
                    `最后更新：${new Date(security.last_updated).toLocaleString()}\n\n` +
                    `🔐 所有敏感信息已采用军工级加密保护\n` +
                    `🚨 实时安全监控已激活\n` +
                    `⚡ 智能频率限制已启用\n` +
                    `🛡️ 多层防护体系运行中`;

                this.updateCookieStatus('success', '国防级认证系统已激活');
                this.announceToScreenReader('国防级认证系统已激活');

                // 🔍 验证认证有效性（通过搜索测试）
                this.updateCookieStatus('info', '验证认证有效性...');
                this.announceToScreenReader('正在验证认证有效性');

                const isValid = await this.validateServerAuth();

                if (isValid) {
                    const successMessage = '认证有效，系统就绪 - 安全等级：国防级';
                    this.updateCookieStatus('success', successMessage);
                    this.announceToScreenReader('认证验证成功，系统已就绪');
                } else {
                    const errorMessage = '认证可能已过期，请联系管理员更新';
                    this.updateCookieStatus('error', errorMessage);
                    this.announceToScreenReader('认证验证失败');
                    this.showRetryOption('loadServerCookies');
                }
            } else {
                const errorMessage = '安全认证配置不可用，请联系管理员';
                this.updateCookieStatus('error', errorMessage);
                this.announceToScreenReader('认证配置不可用');
            }
        } catch (error) {
            console.error('🔥 加载安全认证配置失败:', error);
            const errorMessage = '安全认证配置加载失败，请联系管理员';
            this.updateCookieStatus('error', errorMessage);
            this.announceToScreenReader('认证配置加载失败');
            this.showRetryOption('loadServerCookies');
        }
    }

    /**
     * 显示重试选项
     * @method showRetryOption
     * @param {string} methodName - 要重试的方法名
     * @description 当操作失败时显示重试按钮
     * @returns {void}
     * @since 2.0.0
     */
    showRetryOption(methodName) {
        console.log(`💫 可重试操作: ${methodName}`);

        // 延迟显示重试选项，避免打扰用户
        setTimeout(() => {
            if (confirm('操作失败，是否重试？')) {
                if (typeof this[methodName] === 'function') {
                    this[methodName]();
                }
            }
        }, 2000);
    }

    /**
     * 动画化占位符文本
     * @method animatePlaceholder
     * @param {HTMLInputElement} input - 输入框元素
     * @description 为搜索框添加动态占位符效果
     * @returns {void}
     * @since 2.0.0
     */
    animatePlaceholder(input) {
        const placeholders = [
            '输入搜索关键词...',
            '搜索文档、图片、视频...',
            '支持模糊匹配搜索...',
            '试试搜索文件名...'
        ];

        let currentIndex = 0;

        const updatePlaceholder = () => {
            if (input === document.activeElement) {
                return; // 如果输入框获得焦点，停止动画
            }

            input.placeholder = placeholders[currentIndex];
            currentIndex = (currentIndex + 1) % placeholders.length;
        };

        // 每3秒切换一次占位符
        setInterval(updatePlaceholder, 3000);
    }

    /**
     * 初始化工具提示
     * @method initializeTooltips
     * @description 为界面元素添加工具提示功能
     * @returns {void}
     * @since 2.0.0
     */
    initializeTooltips() {
        const elementsWithTooltips = document.querySelectorAll('[title]');
        elementsWithTooltips.forEach(element => {
            this.enhanceTooltip(element);
        });
    }

    /**
     * 增强工具提示
     * @method enhanceTooltip
     * @param {HTMLElement} element - 要增强的元素
     * @description 为元素添加更好的工具提示体验
     * @returns {void}
     * @since 2.0.0
     */
    enhanceTooltip(element) {
        let tooltipTimeout;

        element.addEventListener('mouseenter', () => {
            tooltipTimeout = setTimeout(() => {
                console.log('💡 显示工具提示:', element.title);
            }, 500);
        });

        element.addEventListener('mouseleave', () => {
            clearTimeout(tooltipTimeout);
        });
    }

    /**
     * 验证服务器端认证有效性
     * @method validateServerAuth
     * @description 通过执行测试搜索来验证服务器认证是否有效
     * @returns {Promise<boolean>} 认证是否有效
     * @throws {Error} 当验证过程失败时抛出错误
     * @since 2.0.0
     */
    async validateServerAuth() {
        try {
            // 通过简单搜索测试服务器端认证是否有效
            const testUrl = '/api/quark/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type:desc,updated_at:desc&_is_hl=1';
            const response = await fetch(testUrl);
            const data = await response.json();
            return data.status === 200 && data.code === 0;
        } catch (error) {
            console.error('认证验证失败:', error);
            return false;
        }
    }

    /**
     * 不再支持本地Cookie加载
     */
    loadLocalCookies() {
        this.updateCookieStatus('info', '请联系管理员配置认证信息');
    }

    /**
     * 禁用Cookie保存功能
     */
    async saveCookies() {
        this.showToast('认证配置由服务器统一管理，如需更新请联系管理员', 'info');
    }

    /**
     * 执行搜索
     */
    async performSearch(page = 1, append = false) {
        const keyword = document.getElementById('searchKeyword').value.trim();

        if (!keyword) {
            this.showToast('请输入搜索关键词', 'error');
            return;
        }

        // 如果是新搜索，重置状态
        if (!append) {
            this.currentKeyword = keyword;
            this.currentPage = 1;
            this.allResults = [];
            this.totalResults = 0;
            this.hasMoreResults = false;
        } else {
            this.currentPage = page;
        }

        if (this.isLoading) return;
        this.isLoading = true;

        const pageSize = parseInt(document.getElementById('pageSize').value);
        const sortType = document.getElementById('sortType').value;
        this.loadMode = document.getElementById('loadMode').value;

        this.showLoading(append ? '加载更多...' : '搜索中...');

        try {
            const results = await this.api.searchFiles(keyword, this.currentPage, pageSize, sortType);
            this.hideLoading();
            this.isLoading = false;

            console.log('搜索API响应:', results); // 调试信息

            if (results.status === 200 && results.code === 0) {
                const newResults = this.api.formatSearchResults(results);
                this.totalResults = results.metadata?._total || results.data?.total || 0;

                console.log('格式化后的结果:', newResults); // 调试信息
                console.log('总结果数:', this.totalResults); // 调试信息

                if (append) {
                    // 追加结果
                    this.allResults = [...this.allResults, ...newResults];
                } else {
                    // 新搜索
                    this.allResults = newResults;
                }

                this.searchResults = newResults;
                this.hasMoreResults = this.allResults.length < this.totalResults;

                this.displaySearchResults(results.data, append);

                if (!append) {
                    this.showToast(`找到 ${this.totalResults} 个结果`, 'success');
                }
            } else {
                console.log('搜索失败，响应:', results); // 调试信息
                this.showToast(`搜索失败: ${results.message || '未知错误'}`, 'error');
            }
        } catch (error) {
            this.hideLoading();
            this.isLoading = false;
            console.error('搜索失败:', error);
            this.showToast(`搜索失败: ${error.message}`, 'error');
        }
    }

    /**
     * 显示搜索结果
     */
    displaySearchResults(data, append = false) {
        const resultsSection = document.getElementById('resultsSection');
        const resultsList = document.getElementById('resultsList');
        const resultsInfo = document.getElementById('resultsInfo');
        const pagination = document.getElementById('pagination');
        const loadMoreContainer = document.getElementById('loadMoreContainer');

        // 显示结果区域
        resultsSection.style.display = 'block';

        // 更新结果信息
        const total = this.totalResults;
        const pageSize = parseInt(document.getElementById('pageSize').value);
        const totalPages = Math.ceil(total / pageSize);

        if (this.loadMode === 'waterfall') {
            resultsInfo.textContent = `已加载 ${this.allResults.length} / ${total} 个文件`;
            pagination.style.display = 'none';
            loadMoreContainer.style.display = this.hasMoreResults ? 'block' : 'none';

            // 更新加载更多按钮信息
            if (this.hasMoreResults) {
                const remaining = total - this.allResults.length;
                document.getElementById('loadMoreInfo').textContent = `还有 ${remaining} 个文件`;
            }
        } else {
            resultsInfo.textContent = `共找到 ${total} 个文件，第 ${this.currentPage} 页（共 ${totalPages} 页）`;
            pagination.style.display = 'block';
            loadMoreContainer.style.display = 'none';
            this.generatePagination(pagination, this.currentPage, totalPages);
        }

        // 处理结果列表
        if (!append) {
            // 新搜索，清空列表
            resultsList.innerHTML = '';
        }

        if (this.searchResults.length === 0 && !append) {
            resultsList.innerHTML = '<div class="no-results">未找到相关文件</div>';
            return;
        }

        // 添加新结果
        const resultsToShow = this.loadMode === 'waterfall' ? this.searchResults : this.searchResults;
        resultsToShow.forEach((file, index) => {
            const resultItem = this.createResultItem(file, index + (append ? this.allResults.length - this.searchResults.length : 0));
            resultsList.appendChild(resultItem);
        });
    }

    /**
     * 创建搜索结果项
     */
    createResultItem(file, index) {
        const div = document.createElement('div');
        div.className = 'result-item';
        div.innerHTML = `
            <div class="result-header">
                <div class="result-name">
                    <i class="${this.api.getFileIcon(file.fileType, file.isFolder)}"></i>
                    ${file.fileName}
                </div>
                <div class="result-actions">
                    ${!file.isFolder ? `<button class="btn btn-download" data-file-id="${file.fileId}" data-file-name="${file.fileName}">
                        <i class="fas fa-download"></i> 下载
                    </button>` : ''}
                </div>
            </div>
            <div class="result-details">
                <div class="result-detail">
                    <i class="fas fa-weight-hanging"></i>
                    <span>大小: ${file.fileSize}</span>
                </div>
                <div class="result-detail">
                    <i class="fas fa-file-alt"></i>
                    <span>类型: ${file.fileType || '未知'}</span>
                </div>
                <div class="result-detail">
                    <i class="fas fa-calendar-plus"></i>
                    <span>创建: ${file.createdAt}</span>
                </div>
                <div class="result-detail">
                    <i class="fas fa-calendar-edit"></i>
                    <span>修改: ${file.updatedAt}</span>
                </div>
                <div class="result-detail">
                    <i class="fas fa-fingerprint"></i>
                    <span>ID: ${file.fileId}</span>
                </div>
                <div class="result-detail">
                    <i class="fas fa-folder"></i>
                    <span>${file.isFolder ? '文件夹' : '文件'}</span>
                </div>
            </div>
        `;

        // 安全地绑定下载事件
        if (!file.isFolder) {
            const downloadBtn = div.querySelector('.btn-download');
            if (downloadBtn) {
                downloadBtn.addEventListener('click', (event) => {
                    // 添加波纹效果
                    this.addRippleEffect(downloadBtn, event);

                    // 添加下载反馈动画
                    downloadBtn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        downloadBtn.style.transform = '';
                    }, 150);

                    // 执行下载
                    this.downloadFile(file.fileId, file.fileName);
                });

                // 添加悬停效果
                downloadBtn.addEventListener('mouseenter', () => {
                    downloadBtn.style.transform = 'translateY(-2px) scale(1.05)';
                });

                downloadBtn.addEventListener('mouseleave', () => {
                    downloadBtn.style.transform = '';
                });
            }
        }

        return div;
    }

    /**
     * 生成分页导航
     */
    generatePagination(container, currentPage, totalPages) {
        container.innerHTML = '';

        if (totalPages <= 1) return;

        // 上一页按钮
        const prevBtn = document.createElement('button');
        prevBtn.textContent = '上一页';
        prevBtn.disabled = currentPage === 1;
        prevBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                this.performSearch(currentPage - 1);
            }
        });
        container.appendChild(prevBtn);

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            const firstBtn = document.createElement('button');
            firstBtn.textContent = '1';
            firstBtn.addEventListener('click', () => this.performSearch(1));
            container.appendChild(firstBtn);

            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                container.appendChild(ellipsis);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.className = i === currentPage ? 'active' : '';
            pageBtn.addEventListener('click', () => this.performSearch(i));
            container.appendChild(pageBtn);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                container.appendChild(ellipsis);
            }

            const lastBtn = document.createElement('button');
            lastBtn.textContent = totalPages;
            lastBtn.addEventListener('click', () => this.performSearch(totalPages));
            container.appendChild(lastBtn);
        }

        // 下一页按钮
        const nextBtn = document.createElement('button');
        nextBtn.textContent = '下一页';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.addEventListener('click', () => {
            if (currentPage < totalPages) {
                this.performSearch(currentPage + 1);
            }
        });
        container.appendChild(nextBtn);
    }

    /**
     * 加载更多结果
     */
    async loadMoreResults() {
        if (!this.hasMoreResults || this.isLoading) return;

        const nextPage = this.currentPage + 1;
        await this.performSearch(nextPage, true);
    }

    /**
     * 更新UI以适应加载模式
     */
    updateUIForLoadMode() {
        const pagination = document.getElementById('pagination');
        const loadMoreContainer = document.getElementById('loadMoreContainer');

        if (this.loadMode === 'waterfall') {
            pagination.style.display = 'none';
            if (this.hasMoreResults) {
                loadMoreContainer.style.display = 'block';
            }
        } else {
            loadMoreContainer.style.display = 'none';
            if (this.totalResults > 0) {
                pagination.style.display = 'block';
            }
        }

        // 如果有搜索结果，重新显示
        if (this.allResults.length > 0) {
            const data = { total: this.totalResults };
            this.displaySearchResults(data, false);
        }
    }

    /**
     * 下载文件
     */
    async downloadFile(fileId, fileName) {
        this.showLoading('获取下载链接...');

        try {
            const response = await this.api.getDownloadUrl([fileId]);
            this.hideLoading();

            const downloadInfo = this.api.extractDownloadInfo(response);

            if (downloadInfo.length > 0) {
                const info = downloadInfo[0];
                const downloadUrl = info.downloadUrl;

                if (downloadUrl) {
                    // 添加到下载管理
                    this.addDownload(fileId, fileName, 'pending');

                    this.showLoading('正在下载文件...');

                    try {
                        // 调用异步下载方法
                        await this.api.downloadFile(downloadUrl, fileName);

                        this.hideLoading();
                        this.updateDownload(fileId, 'success');
                        this.showToast(`下载完成: ${fileName}`, 'success');
                    } catch (downloadError) {
                        this.hideLoading();
                        console.error('下载文件失败:', downloadError);
                        this.updateDownload(fileId, 'error');

                        // 检查是否是Cookie过期错误
                        if (downloadError.message.includes('auth expired') ||
                            downloadError.message.includes('require login') ||
                            downloadError.message.includes('auth miss')) {
                            this.showCookieExpiredAlert();
                        } else {
                            this.showToast(`下载失败: ${downloadError.message}`, 'error');
                        }
                    }
                } else {
                    this.showToast('获取下载链接失败', 'error');
                }
            } else {
                this.showToast('无法获取下载信息', 'error');
            }
        } catch (error) {
            this.hideLoading();
            console.error('下载失败:', error);
            this.showToast(`下载失败: ${error.message}`, 'error');
        }
    }

    /**
     * 显示Cookie过期提醒
     */
    showCookieExpiredAlert() {
        const alert = document.getElementById('cookieExpiredAlert');
        alert.style.display = 'flex';
    }

    /**
     * 关闭Cookie过期提醒
     */
    dismissCookieAlert() {
        const alert = document.getElementById('cookieExpiredAlert');
        alert.style.display = 'none';
    }

    /**
     * 添加下载任务
     */
    addDownload(fileId, fileName, status) {
        this.downloads.set(fileId, {
            id: fileId,
            name: fileName,
            status: status,
            timestamp: Date.now()
        });
        this.updateDownloadsList();
    }

    /**
     * 更新下载状态
     */
    updateDownload(fileId, status) {
        if (this.downloads.has(fileId)) {
            const download = this.downloads.get(fileId);
            download.status = status;
            this.updateDownloadsList();
        }
    }

    /**
     * 更新下载列表显示
     */
    updateDownloadsList() {
        const downloadsSection = document.getElementById('downloadsSection');
        const downloadsList = document.getElementById('downloadsList');

        if (this.downloads.size === 0) {
            downloadsSection.style.display = 'none';
            return;
        }

        downloadsSection.style.display = 'block';
        downloadsList.innerHTML = '';

        Array.from(this.downloads.values())
            .sort((a, b) => b.timestamp - a.timestamp)
            .forEach(download => {
                const item = this.createDownloadItem(download);
                downloadsList.appendChild(item);
            });
    }

    /**
     * 创建下载项目
     */
    createDownloadItem(download) {
        const div = document.createElement('div');
        div.className = 'download-item';
        div.innerHTML = `
            <div class="download-header">
                <div class="download-name">${download.name}</div>
                <div class="download-status ${download.status}">${this.getStatusText(download.status)}</div>
            </div>
        `;
        return div;
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            pending: '准备中',
            success: '已完成',
            error: '失败'
        };
        return statusMap[status] || '未知';
    }

    /**
     * 显示加载指示器
     */
    showLoading(message = '加载中...') {
        const loading = document.getElementById('loading');
        const loadingText = loading.querySelector('p');
        loadingText.textContent = message;
        loading.style.display = 'flex';
    }

    /**
     * 隐藏加载指示器
     */
    hideLoading() {
        const loading = document.getElementById('loading');
        loading.style.display = 'none';
    }

    /**
     * 显示消息提示 - 现代化版本
     */
    showToast(message, type = 'info') {
        // 移除现有的toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        // 添加图标
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle'
        };

        toast.innerHTML = `
            <i class="${icons[type] || icons.info}"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(toast);

        // 添加音效提示（可选）
        this.playNotificationSound(type);

        // 显示动画
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 400);
        }, 4000);

        // 添加点击关闭功能
        toast.addEventListener('click', () => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 400);
        });
    }

    /**
     * 播放通知音效
     */
    playNotificationSound(type) {
        // 创建Web Audio API音效
        if ('AudioContext' in window || 'webkitAudioContext' in window) {
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            const audioContext = new AudioContext();

            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // 根据类型设置不同音调
            const frequencies = {
                success: [523.25, 659.25], // C5, E5
                error: [415.30, 369.99],   // G#4, F#4
                info: [523.25],            // C5
                warning: [466.16, 523.25] // A#4, C5
            };

            const freq = frequencies[type] || frequencies.info;

            oscillator.frequency.setValueAtTime(freq[0], audioContext.currentTime);
            if (freq[1]) {
                oscillator.frequency.setValueAtTime(freq[1], audioContext.currentTime + 0.1);
            }

            oscillator.type = 'sine';
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        }
    }

    /**
     * 添加按钮点击波纹效果
     */
    addRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;

        // 添加波纹动画样式
        if (!document.querySelector('#ripple-styles')) {
            const rippleStyles = document.createElement('style');
            rippleStyles.id = 'ripple-styles';
            rippleStyles.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
                .btn {
                    position: relative;
                    overflow: hidden;
                }
            `;
            document.head.appendChild(rippleStyles);
        }

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * 更新Cookie状态显示
     */
    updateCookieStatus(type, message) {
        const statusElement = document.getElementById('cookieStatus');
        statusElement.className = `cookie-status ${type}`;
        statusElement.innerHTML = `<i class="fas fa-${this.getStatusIcon(type)}"></i> ${message}`;
    }

    /**
     * 获取状态图标
     */
    getStatusIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// 全局应用实例
let app;

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app = new QuarkApp();
});