ExpressSlowDownWarning: The behaviour of the 'delayMs' option was changed in express-slow-down v2:
- For the old behavior, change the delayMs option to:

  delayMs: (used, req) => {
	  const delayAfter = req.slowDown.limit;
	  return (used - delayAfter) * 500;
  },

- For the new behavior, change the delayMs option to:

	delayMs: () => 500,

Or set 'options.validate: {delayMs: false}' to disable this message. See https://express-rate-limit.github.io/WRN_ESD_DELAYMS/ for more information.
    at slowDown (/www/wwwroot/sk.djxs.xyz/node_modules/express-slow-down/dist/index.cjs:78:18)
    at Object.<anonymous> (/www/wwwroot/sk.djxs.xyz/src/server/main.js:71:22)
    at Module._compile (node:internal/modules/cjs/loader:1198:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1252:10)
    at Module.load (node:internal/modules/cjs/loader:1076:32)
    at Function.Module._load (node:internal/modules/cjs/loader:911:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:81:12)
    at node:internal/main/run_main_module:22:47 {
  code: 'WRN_ESD_DELAYMS',
  help: 'https://express-rate-limit.github.io/WRN_ESD_DELAYMS/'
}
🔐 Cookie已加密加载到安全存储
(node:11885) [DEP0106] DeprecationWarning: crypto.createCipher is deprecated.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:11885) Warning: Use Cipheriv for counter mode of aes-256-gcm

🛡️ 国防级安全代理服务器已启动
📍 本地访问: http://localhost:3000
🔗 安全API: http://localhost:3000/api/quark/*
💊 健康检查: http://localhost:3000/api/health
📊 安全统计: http://localhost:3000/api/security/stats
🔐 认证状态: ✅ 已加密加载
🧠 初始化智能安全监控系统...
🗄️ 初始化数据库连接池...
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: reconnect. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
✅ 数据库连接成功
✅ 数据库表结构初始化完成
📝 已加载 0 个黑名单IP到缓存
🧹 过期记录清理完成
✅ 智能安全监控系统已启动
🧠 智能安全监控: ✅ 已激活
⚡ 频率限制: ✅ 已启用
🛡️ 安全等级: 国防级智能型

服务器运行在端口 3000 - 智能安全模式
✅ 白名单IP访问: **************
ValidationError: The Express 'trust proxy' setting is true, which allows anyone to trivially bypass IP-based rate limiting. See https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/ for more information.
    at Object.trustProxy (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:169:13)
    at Object.wrappedValidations.<computed> [as trustProxy] (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:398:22)
    at Object.keyGenerator (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:670:20)
    at /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:724:32
    at processTicksAndRejections (node:internal/process/task_queues:96:5)
    at async /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:704:5 {
  code: 'ERR_ERL_PERMISSIVE_TRUST_PROXY',
  help: 'https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/'
}
👋 欢迎新访客: ************** (基础信任度: 50)
🔍 [WARNING] ************** → / (信任度: 65)
✅ 白名单IP访问: **************
ValidationError: The Express 'trust proxy' setting is true, which allows anyone to trivially bypass IP-based rate limiting. See https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/ for more information.
    at Object.trustProxy (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:169:13)
    at Object.wrappedValidations.<computed> [as trustProxy] (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:398:22)
    at Object.keyGenerator (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:670:20)
    at /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:724:32
    at async /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:704:5 {
  code: 'ERR_ERL_PERMISSIVE_TRUST_PROXY',
  help: 'https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/'
}
🔍 [WARNING] ************** → /api/security/stats (信任度: 80)
👋 欢迎新访客: *************** (基础信任度: 50)
🔍 [INFO] *************** → / (信任度: 55)
ValidationError: The Express 'trust proxy' setting is true, which allows anyone to trivially bypass IP-based rate limiting. See https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/ for more information.
    at Object.trustProxy (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:169:13)
    at Object.wrappedValidations.<computed> [as trustProxy] (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:398:22)
    at Object.keyGenerator (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:670:20)
    at /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:724:32
    at async /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:704:5 {
  code: 'ERR_ERL_PERMISSIVE_TRUST_PROXY',
  help: 'https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/'
}
🔍 [INFO] *************** → /api/cookies (信任度: 60)
❓ 404错误 [IP: ***************]: /favicon.ico
🔍 [INFO] *************** → /favicon.ico (信任度: 65)
ValidationError: The Express 'trust proxy' setting is true, which allows anyone to trivially bypass IP-based rate limiting. See https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/ for more information.
    at Object.trustProxy (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:169:13)
    at Object.wrappedValidations.<computed> [as trustProxy] (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:398:22)
    at Object.keyGenerator (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:670:20)
    at /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:724:32
    at async /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:704:5 {
  code: 'ERR_ERL_PERMISSIVE_TRUST_PROXY',
  help: 'https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/'
}
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: ***************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] *************** → /api/quark/1/clouddrive/file/search (信任度: 70)
📥 安全响应: 200 OK [ID: 8c41e92a30b68e27]
✅ 安全请求成功 [ID: 8c41e92a30b68e27]
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E9%AD%94%E9%81%93%E7%A5%96%E5%B8%88&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: ***************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] *************** → /api/quark/1/clouddrive/file/search (信任度: 75)
📥 安全响应: 200 OK [ID: 752bd223ea580927]
✅ 安全请求成功 [ID: 752bd223ea580927]
✅ 白名单IP访问: **************
🔍 [INFO] ************** → /api/security/stats (信任度: 85)
✅ 白名单IP访问: **************
🔍 [INFO] ************** → / (信任度: 90)
✅ 白名单IP访问: **************
ValidationError: The Express 'trust proxy' setting is true, which allows anyone to trivially bypass IP-based rate limiting. See https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/ for more information.
    at Object.trustProxy (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:169:13)
    at Object.wrappedValidations.<computed> [as trustProxy] (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:398:22)
    at Object.keyGenerator (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:670:20)
    at /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:724:32
    at async /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:704:5 {
  code: 'ERR_ERL_PERMISSIVE_TRUST_PROXY',
  help: 'https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/'
}
🔍 [INFO] ************** → /api/health (信任度: 95)
✅ 白名单IP访问: **************
🔍 [INFO] ************** → /css/styles.css (信任度: 100)
✅ 白名单IP访问: **************
🔍 [INFO] ************** → /js/app.js (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/health (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/health (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/health (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/health (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/health (信任度: 100)
✅ 白名单IP访问: **************
🔍 [INFO] ************** → /api/security/stats (信任度: 100)
❓ 404错误 [IP: *************]: /404.html
👋 欢迎新访客: ************* (基础信任度: 50)
🔍 [INFO] ************* → /404.html (信任度: 55)
👋 欢迎新访客: ************* (基础信任度: 50)
🔍 [INFO] ************* → / (信任度: 55)
🔍 [INFO] ************* → /api/cookies (信任度: 60)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 65)
📥 安全响应: 200 OK [ID: c3125e161543c5ea]
✅ 安全请求成功 [ID: c3125e161543c5ea]
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E9%AD%94%E9%81%93%E7%A5%96%E5%B8%88&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 70)
📥 安全响应: 200 OK [ID: a039eb34dbcccead]
✅ 安全请求成功 [ID: a039eb34dbcccead]
✅ 白名单IP访问: **************
🔍 [INFO] ************** → /api/health (信任度: 100)
✅ 白名单IP访问: **************
🔍 [INFO] ************** → /api/cookies (信任度: 100)
✅ 白名单IP访问: **************
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1 [IP: **************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************** → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: 91e293d5f86a6a19]
✅ 安全请求成功 [ID: 91e293d5f86a6a19]
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/security/stats (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/security/stats (信任度: 100)
❓ 404错误 [IP: *************]: /api/security/stat
🔍 [INFO] ************* → /api/security/stat (信任度: 75)
❓ 404错误 [IP: *************]: /api/
🔍 [INFO] ************* → /api/ (信任度: 80)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /js/quark-api.js (信任度: 100)
✅ 白名单IP访问: **************
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=5&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: **************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [WARNING] ************** → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: df09d8d4caaac41b]
✅ 安全请求成功 [ID: df09d8d4caaac41b]
🔍 [INFO] ************* → / (信任度: 85)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /styles.css (信任度: 90)
🔍 [INFO] ************* → /quark-api.js (信任度: 95)
🔍 [INFO] ************* → /app.js (信任度: 95)
❓ 404错误 [IP: *************]: /favicon.ico
🔍 [INFO] ************* → /favicon.ico (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
❓ 404错误 [IP: *************]: /app.js
🔍 [INFO] ************* → /styles.css (信任度: 100)
🔍 [INFO] ************* → /quark-api.js (信任度: 100)
🔍 [INFO] ************* → /app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
❓ 404错误 [IP: *************]: /styles.css
❓ 404错误 [IP: *************]: /quark-api.js
🔍 [WARNING] ************* → /quark-api.js (信任度: 100)
🔍 [WARNING] ************* → /styles.css (信任度: 100)
🔍 [WARNING] ************* → /app.js (信任度: 100)
❓ 404错误 [IP: *************]: /app.js
🔍 [WARNING] ************* → /favicon.ico (信任度: 100)
❓ 404错误 [IP: *************]: /favicon.ico
✅ 白名单IP访问: **************
🔄 安全代理请求: POST https://drive-pc.quark.cn/1/clouddrive/file/download?pr=ucpro&fr=pc&uc_param_str= [IP: **************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [WARNING] ************** → /api/quark/1/clouddrive/file/download (信任度: 100)
📥 安全响应: 200 OK [ID: 31c43a026c94ae62]
✅ 安全请求成功 [ID: 31c43a026c94ae62]
✅ 白名单IP访问: **************
ValidationError: The Express 'trust proxy' setting is true, which allows anyone to trivially bypass IP-based rate limiting. See https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/ for more information.
    at Object.trustProxy (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:169:13)
    at Object.wrappedValidations.<computed> [as trustProxy] (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:398:22)
    at Object.keyGenerator (/www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:670:20)
    at /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:724:32
    at runMicrotasks (<anonymous>)
    at processTicksAndRejections (node:internal/process/task_queues:96:5)
    at async /www/wwwroot/sk.djxs.xyz/node_modules/express-rate-limit/dist/index.cjs:704:5 {
  code: 'ERR_ERL_PERMISSIVE_TRUST_PROXY',
  help: 'https://express-rate-limit.github.io/ERR_ERL_PERMISSIVE_TRUST_PROXY/'
}
🔽 安全下载请求: [IP: **************]
🍪 下载使用加密认证
🔍 [WARNING] ************** → /api/download/file (信任度: 100)
📥 下载响应: 200 OK
✅ 开始安全文件传输
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → / (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /css/styles.css (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → / (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
🔍 [INFO] ************* → /css/styles.css (信任度: 100)
🔍 [INFO] ************* → /js/quark-api.js (信任度: 100)
🔍 [INFO] ************* → /js/app.js (信任度: 100)
🔍 [WARNING] ************* → /api/cookies (信任度: 100)
🔍 [WARNING] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [WARNING] ************* → /favicon.ico (信任度: 100)
❓ 404错误 [IP: *************]: /favicon.ico
📥 安全响应: 200 OK [ID: 080c55d984236e4a]
✅ 安全请求成功 [ID: 080c55d984236e4a]
✅ 白名单IP访问: **************
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: **************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************** → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: a6fc931070dc4571]
✅ 安全请求成功 [ID: a6fc931070dc4571]
✅ 白名单IP访问: **************
🔄 安全代理请求: POST https://drive-pc.quark.cn/1/clouddrive/file/download?pr=ucpro&fr=pc&uc_param_str= [IP: **************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************** → /api/quark/1/clouddrive/file/download (信任度: 100)
📥 安全响应: 200 OK [ID: be1de5f9f9f296c0]
✅ 安全请求成功 [ID: be1de5f9f9f296c0]
✅ 白名单IP访问: **************
🔽 安全下载请求: [IP: **************]
🍪 下载使用加密认证
🔍 [INFO] ************** → /api/download/file (信任度: 100)
📥 下载响应: 200 OK
✅ 开始安全文件传输
🔍 [INFO] ************* → / (信任度: 100)
🔍 [INFO] ************* → /css/styles.css (信任度: 100)
🔍 [INFO] ************* → /js/quark-api.js (信任度: 100)
🔍 [INFO] ************* → /js/app.js (信任度: 100)
🔍 [INFO] ************* → /api/cookies (信任度: 100)
🔍 [INFO] ************* → /favicon.ico (信任度: 100)
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
❓ 404错误 [IP: *************]: /favicon.ico
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
📥 安全响应: 200 OK [ID: d56a341edbb0d95c]
✅ 安全请求成功 [ID: d56a341edbb0d95c]
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E9%80%9A%E8%BF%87%E9%87%87%E8%AE%BF&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
📥 安全响应: 200 OK [ID: 5acb2b316356567b]
✅ 安全请求成功 [ID: 5acb2b316356567b]
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E5%A4%A9%E5%AE%98%E8%B5%90%E7%A6%8F&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
📥 安全响应: 200 OK [ID: 332bb5dcc7a389c9]
✅ 安全请求成功 [ID: 332bb5dcc7a389c9]
🔍 [INFO] ************* → / (信任度: 100)
🔍 [INFO] ************* → /css/styles.css (信任度: 100)
🔍 [INFO] ************* → /js/quark-api.js (信任度: 100)
🔍 [INFO] ************* → /js/app.js (信任度: 100)
🔍 [INFO] ************* → /api/cookies (信任度: 100)
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
📥 安全响应: 200 OK [ID: ef12bcdfcf71438f]
✅ 安全请求成功 [ID: ef12bcdfcf71438f]
🔍 [INFO] ************* → /css/styles.css (信任度: 100)
🔍 [INFO] ************* → /js/quark-api.js (信任度: 100)
🔍 [INFO] ************* → /js/app.js (信任度: 100)
🔍 [INFO] ************* → /api/cookies (信任度: 100)
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
🔍 [INFO] ************* → /favicon.ico (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
❓ 404错误 [IP: *************]: /favicon.ico
📥 安全响应: 200 OK [ID: 9581575a7f35fa2d]
✅ 安全请求成功 [ID: 9581575a7f35fa2d]
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E9%80%9A%E8%BF%87%E9%87%87%E8%AE%BF&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
📥 安全响应: 200 OK [ID: 98565007dc756c30]
✅ 安全请求成功 [ID: 98565007dc756c30]
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E5%A4%A9%E5%AE%98%E8%B5%90%E7%A6%8F&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
📥 安全响应: 200 OK [ID: 931e6273fa10febe]
✅ 安全请求成功 [ID: 931e6273fa10febe]
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → / (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /js/app.js (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → / (信任度: 100)
✅ 白名单IP访问: **************
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: **************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************** → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: 2b7d06044355df6e]
✅ 安全请求成功 [ID: 2b7d06044355df6e]
✅ 白名单IP访问: **************
🔄 安全代理请求: POST https://drive-pc.quark.cn/1/clouddrive/file/download?pr=ucpro&fr=pc&uc_param_str= [IP: **************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************** → /api/quark/1/clouddrive/file/download (信任度: 100)
📥 安全响应: 200 OK [ID: 24c233c59093c465]
✅ 安全请求成功 [ID: 24c233c59093c465]
✅ 白名单IP访问: **************
🔽 安全下载请求: [IP: **************]
🍪 下载使用加密认证
🔍 [INFO] ************** → /api/download/file (信任度: 100)
📥 下载响应: 200 OK
✅ 开始安全文件传输
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /js/app.js (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /js/app.js (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /js/app.js (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
🔍 [INFO] ************* → /js/quark-api.js (信任度: 100)
🔍 [INFO] ************* → /css/styles.css (信任度: 100)
🔍 [INFO] ************* → /js/app.js (信任度: 100)
🔍 [INFO] ************* → /api/cookies (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
❓ 404错误 [IP: *************]: /favicon.ico
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
🔍 [INFO] ************* → /favicon.ico (信任度: 100)
📥 安全响应: 200 OK [ID: edb64a4a38b93298]
✅ 安全请求成功 [ID: edb64a4a38b93298]
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E5%A4%A9%E5%AE%98%E8%B5%90%E7%A6%8F&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: e9235c992f58aa10]
✅ 安全请求成功 [ID: e9235c992f58aa10]
🔄 安全代理请求: POST https://drive-pc.quark.cn/1/clouddrive/file/download?pr=ucpro&fr=pc&uc_param_str= [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/download (信任度: 100)
📥 安全响应: 200 OK [ID: a2a50246dd3b351d]
✅ 安全请求成功 [ID: a2a50246dd3b351d]
🔽 安全下载请求: [IP: *************]
🍪 下载使用加密认证
🔍 [INFO] ************* → /api/download/file (信任度: 100)
📥 下载响应: 200 OK
✅ 开始安全文件传输
🔄 安全代理请求: POST https://drive-pc.quark.cn/1/clouddrive/file/download?pr=ucpro&fr=pc&uc_param_str= [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/download (信任度: 100)
📥 安全响应: 200 OK [ID: 45e188c6f73eabcb]
✅ 安全请求成功 [ID: 45e188c6f73eabcb]
🔽 安全下载请求: [IP: *************]
🍪 下载使用加密认证
🔍 [INFO] ************* → /api/download/file (信任度: 100)
📥 下载响应: 200 OK
✅ 开始安全文件传输
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/health (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
🔍 [INFO] ************* → /js/quark-api.js (信任度: 100)
🔍 [INFO] ************* → /css/styles.css (信任度: 100)
🔍 [INFO] ************* → /js/app.js (信任度: 100)
🔍 [INFO] ************* → /api/cookies (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: 713728e3a53479bf]
✅ 安全请求成功 [ID: 713728e3a53479bf]
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → / (信任度: 100)
❓ 404错误 [IP: *************]: /404.html
❓ 404错误 [IP: *************]: /404.html
🔍 [INFO] ************* → /404.html (信任度: 60)
🔍 [INFO] ************* → /404.html (信任度: 60)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → / (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/health (信任度: 100)
✅ 白名单IP访问: **************
🔍 [WARNING] ************** → /api/health (信任度: 100)
🔍 [INFO] ************* → / (信任度: 100)
🔍 [INFO] ************* → /css/styles.css (信任度: 100)
🔍 [INFO] ************* → /js/quark-api.js (信任度: 100)
🔍 [INFO] ************* → /js/app.js (信任度: 100)
🔍 [INFO] ************* → /api/cookies (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: 630cfcf09aaf710f]
✅ 安全请求成功 [ID: 630cfcf09aaf710f]
❓ 404错误 [IP: *************]: /favicon.ico
🔍 [INFO] ************* → /favicon.ico (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E9%AD%94%E9%81%93%E7%A5%96%E5%B8%88&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: 4b598a0f8214d0b0]
✅ 安全请求成功 [ID: 4b598a0f8214d0b0]
🔄 安全代理请求: POST https://drive-pc.quark.cn/1/clouddrive/file/download?pr=ucpro&fr=pc&uc_param_str= [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/download (信任度: 100)
📥 安全响应: 200 OK [ID: 1c6413a2b3aa4071]
✅ 安全请求成功 [ID: 1c6413a2b3aa4071]
🔽 安全下载请求: [IP: *************]
🍪 下载使用加密认证
🔍 [INFO] ************* → /api/download/file (信任度: 100)
📥 下载响应: 200 OK
✅ 开始安全文件传输
🔍 [INFO] ************* → / (信任度: 100)
🔍 [INFO] ************* → /css/styles.css (信任度: 100)
🔍 [INFO] ************* → /js/quark-api.js (信任度: 100)
🔍 [INFO] ************* → /js/app.js (信任度: 100)
🔍 [INFO] ************* → /api/cookies (信任度: 100)
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=test&_page=1&_size=1&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: 36ec5a5505f3f863]
✅ 安全请求成功 [ID: 36ec5a5505f3f863]
🔄 安全代理请求: GET https://drive-pc.quark.cn/1/clouddrive/file/search?pr=ucpro&fr=pc&uc_param_str=&q=%E5%A4%A9%E5%AE%98%E8%B5%90%E7%A6%8F&_page=1&_size=50&_fetch_total=1&_sort=file_type%3Adesc%2Cupdated_at%3Adesc&_is_hl=1 [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/search (信任度: 100)
📥 安全响应: 200 OK [ID: 8c106385abcece46]
✅ 安全请求成功 [ID: 8c106385abcece46]
🔄 安全代理请求: POST https://drive-pc.quark.cn/1/clouddrive/file/download?pr=ucpro&fr=pc&uc_param_str= [IP: *************]
🍪 使用加密认证: 5abd07ca18a353175d3fc0c53be589be...
🔍 [INFO] ************* → /api/quark/1/clouddrive/file/download (信任度: 100)
📥 安全响应: 200 OK [ID: d85d42c24549af44]
✅ 安全请求成功 [ID: d85d42c24549af44]
🔽 安全下载请求: [IP: *************]
🍪 下载使用加密认证
🔍 [INFO] ************* → /api/download/file (信任度: 100)
📥 下载响应: 200 OK
✅ 开始安全文件传输
