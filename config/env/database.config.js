/**
 * 🗄️ 数据库配置
 * Database Configuration
 */
module.exports = {
    // MySQL数据库配置
    mysql: {
        host: 'localhost',
        port: 3306,
        user: 'sk',
        password: 'GYmmnkCaaWmr5Kea',
        database: 'sk',
        charset: 'utf8mb4',
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000,
        reconnect: true
    },
    
    // 表名配置
    tables: {
        access_logs: 'security_access_logs',
        ip_blacklist: 'security_ip_blacklist', 
        user_sessions: 'security_user_sessions',
        threat_records: 'security_threat_records'
    }
}; 